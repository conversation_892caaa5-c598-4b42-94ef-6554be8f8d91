"use client"

import { useEffect, useRef, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Image from "next/image"
import Link from "next/link"
import {
  Building2,
  MapPin,
  Bed,
  Square,
  Star,
  Calendar,
  Camera,
  Home,
  Sparkles,
  Building,
  Search,
  Filter,
  X,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { newProjects, resaleProperties } from "./projects-data"

// Use the imported data instead of local arrays
// Projects data is now imported from projects-data.ts

export default function ProjectsPageContent() {
  const mainRef = useRef<HTMLElement>(null)
  const [activeTab, setActiveTab] = useState("new-projects")
  const [searchTerm, setSearchTerm] = useState("")
  const [priceFilter, setPriceFilter] = useState("all")
  const [locationFilter, setLocationFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [bhkFilter, setBhkFilter] = useState("all")
  const [projectTypeFilter, setProjectTypeFilter] = useState("all")
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 9

  // Prevent any layout shifts by locking body width
  useEffect(() => {
    const preventLayoutShift = () => {
      // Lock the body width to prevent any shifts
      const currentWidth = document.body.clientWidth
      document.body.style.width = `${currentWidth}px`
      document.body.style.overflowX = 'hidden'

      // Also lock the main container
      if (mainRef.current) {
        const containerWidth = mainRef.current.clientWidth
        mainRef.current.style.width = `${containerWidth}px`
        mainRef.current.style.maxWidth = '100vw'
      }
    }

    const restoreLayout = () => {
      document.body.style.width = ''
      document.body.style.overflowX = ''

      if (mainRef.current) {
        mainRef.current.style.width = ''
        mainRef.current.style.maxWidth = ''
      }
    }

    if (showFilters) {
      preventLayoutShift()
    } else {
      // Small delay to ensure smooth transition
      setTimeout(restoreLayout, 300)
    }

    return () => {
      restoreLayout()
    }
  }, [showFilters])

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll:not(.project-card)")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  // Filter function
  const filterProperties = (properties: any[]) => {
    return properties.filter((property) => {
      const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           property.location.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesPrice = priceFilter === "all" ||
                          (priceFilter === "under-1cr" && property.price.includes("L")) ||
                          (priceFilter === "1-2cr" && (property.price.includes("1.") || property.price.includes("2."))) ||
                          (priceFilter === "above-2cr" && (property.price.includes("2.5") || property.price.includes("3.") || property.price.includes("4.")))

      const matchesLocation = locationFilter === "all" ||
                             property.location.toLowerCase().includes(locationFilter.toLowerCase())

      const matchesType = typeFilter === "all" ||
                         (typeFilter === "residential" && property.beds.includes("BHK")) ||
                         (typeFilter === "commercial" && (property.beds.includes("Office") || property.beds.includes("commercial"))) ||
                         (typeFilter === "villa" && property.beds.includes("Villa"))

      const matchesBhk = bhkFilter === "all" ||
                        (bhkFilter === "1bhk" && property.beds.includes("1")) ||
                        (bhkFilter === "2bhk" && property.beds.includes("2")) ||
                        (bhkFilter === "3bhk" && property.beds.includes("3")) ||
                        (bhkFilter === "4bhk" && property.beds.includes("4")) ||
                        (bhkFilter === "5bhk" && property.beds.includes("5"))

      const matchesProjectType = projectTypeFilter === "all" ||
                                (projectTypeFilter === "new-launch" && property.type === "New Launch") ||
                                (projectTypeFilter === "ready-to-move" && property.type === "Ready to Move") ||
                                (projectTypeFilter === "commercial" && property.type === "Commercial") ||
                                (projectTypeFilter === "pre-launch" && property.status === "Pre-Launch")

      return matchesSearch && matchesPrice && matchesLocation && matchesType && matchesBhk && matchesProjectType
    })
  }

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("")
    setPriceFilter("all")
    setLocationFilter("all")
    setTypeFilter("all")
    setBhkFilter("all")
    setProjectTypeFilter("all")
    setCurrentPage(1)
  }

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [searchTerm, priceFilter, locationFilter, typeFilter, bhkFilter, projectTypeFilter, activeTab])

  // Handle page change with scroll
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage)

    // Small delay to ensure content is updated, then scroll
    setTimeout(() => {
      const tabHeading = document.querySelector('.tab-content-heading')
      if (tabHeading) {
        const offset = 120 // Offset from top for better visibility
        const elementPosition = tabHeading.getBoundingClientRect().top
        const offsetPosition = elementPosition + window.pageYOffset - offset

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        })
      } else {
        // Fallback: scroll to projects section
        const projectsSection = document.querySelector('.projects-grid-section')
        if (projectsSection) {
          projectsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        }
      }
    }, 100)
  }

  // Pagination logic
  const getPaginatedData = (data: any[]) => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return data.slice(startIndex, endIndex)
  }

  const getTotalPages = (dataLength: number) => {
    return Math.ceil(dataLength / itemsPerPage)
  }

  // Pagination component
  const renderPagination = (totalItems: number) => {
    const totalPages = getTotalPages(totalItems)

    if (totalPages <= 1) return null

    const getPageNumbers = () => {
      const pages = []
      const maxVisiblePages = 5

      if (totalPages <= maxVisiblePages) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        if (currentPage <= 3) {
          for (let i = 1; i <= 4; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        } else if (currentPage >= totalPages - 2) {
          pages.push(1)
          pages.push('...')
          for (let i = totalPages - 3; i <= totalPages; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        }
      }

      return pages
    }

    return (
      <div className="flex justify-center items-center mt-12 mb-8">
        <div className="flex items-center space-x-2">
          {/* Previous Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="rounded-full border-2 border-gray-200 hover:border-burgundy-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft size={16} />
          </Button>

          {/* Page Numbers */}
          {getPageNumbers().map((page, index) => (
            <div key={index}>
              {page === '...' ? (
                <span className="px-3 py-2 text-gray-400">...</span>
              ) : (
                <Button
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page as number)}
                  className={`rounded-full min-w-[40px] h-10 ${
                    currentPage === page
                      ? 'bg-burgundy-600 hover:bg-burgundy-700 text-white border-burgundy-600'
                      : 'border-2 border-gray-200 hover:border-burgundy-300 hover:bg-burgundy-50'
                  }`}
                >
                  {page}
                </Button>
              )}
            </div>
          ))}

          {/* Next Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="rounded-full border-2 border-gray-200 hover:border-burgundy-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronRight size={16} />
          </Button>
        </div>
      </div>
    )
  }

  // WhatsApp inquiry function
  const handleWhatsAppInquiry = (propertyName: string, location: string, price: string, propertySlug: string) => {
    const phoneNumber = "918141112929"
    const propertyLink = `${window.location.origin}/projects/${propertySlug}`
    const message = `Hi! I'm interested in *${propertyName}* located at ${location}.

Property Details:
Location: ${location}
Price: ${price}
Property Link: ${propertyLink}

Could you please share more details about this property? I would like to schedule a site visit.`

    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const renderProjectCard = (project: any, index: number) => (
    <Card
      key={`${activeTab}-${project.id}-${searchTerm}-${priceFilter}-${locationFilter}-${typeFilter}-${bhkFilter}-${projectTypeFilter}`}
      className="group hover:shadow-xl transition-all duration-300 border-0 bg-white overflow-hidden hover:-translate-y-2 will-change-transform animate-in fade-in slide-in-from-bottom-4 duration-500"
      style={{
        animationDelay: `${index * 80}ms`
      }}
    >
      <div className="relative overflow-hidden">
        <Image
          src={project.image || "/placeholder.svg"}
          alt={project.title}
          width={400}
          height={300}
          className="w-full h-48 lg:h-64 object-cover group-hover:scale-110 transition-transform duration-300 ease-out will-change-transform"
        />
        <div className="absolute top-3 left-3 flex gap-2 z-20">
          <Badge
            className={`text-xs border-0 shadow-sm transition-transform duration-200 ${
              project.type === "New Launch"
                ? "bg-green-500 text-white"
                : project.type === "Ready to Move"
                  ? "bg-blue-500 text-white"
                  : "bg-purple-500 text-white"
            }`}
          >
            {project.type}
          </Badge>
          <Badge variant="secondary" className="bg-white/90 text-gray-700 text-xs border-0 shadow-sm">
            {project.status}
          </Badge>
        </div>
        <div className="absolute top-3 right-3 bg-white/90 rounded-full px-2 py-1 flex items-center space-x-1 shadow-sm z-20">
          <Star className="text-yellow-500 fill-current" size={12} />
          <span className="text-xs font-medium text-gray-800">{project.rating}</span>
        </div>
        {/* Photo Count Indicator */}
        <div className="absolute bottom-3 right-3 bg-black/70 rounded-full px-2 py-1 flex items-center space-x-1 shadow-sm z-20">
          <Camera className="text-white" size={12} />
          <span className="text-xs font-medium text-white">{project.photoCount}</span>
        </div>
      </div>

      <CardContent className="p-5 lg:p-6">
        <div className="mb-4">
          <h3 className="text-lg lg:text-2xl font-bold text-gray-900 mb-2 group-hover:text-burgundy-600 transition-colors">
            {project.title}
          </h3>
          <div className="flex items-center text-gray-500 mb-3">
            <MapPin size={14} className="mr-2" />
            <span className="text-sm">{project.location}</span>
          </div>
          <div className="text-xl lg:text-2xl font-bold text-burgundy-600 mb-4">{project.price}</div>
        </div>

        {/* Property Details - Real Estate Style */}
        <div className="space-y-3 mb-4 lg:mb-6">
          {/* Property Type */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              {project.beds.toLowerCase().includes('office') || project.beds.toLowerCase().includes('commercial') ? (
                <Building className="text-gray-400 mr-2" size={16} />
              ) : (
                <Bed className="text-gray-400 mr-2" size={16} />
              )}
              <span className="text-sm font-medium text-gray-700">Type</span>
            </div>
            <span className="text-sm font-bold text-gray-900">{project.beds}</span>
          </div>

          {/* Size */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              <Square className="text-gray-400 mr-2" size={16} />
              <span className="text-sm font-medium text-gray-700">Size</span>
            </div>
            <span className="text-sm font-bold text-gray-900">{project.area}</span>
          </div>

          {/* Possession */}
          <div className="flex items-center justify-between p-3 bg-burgundy-50 rounded-lg">
            <div className="flex items-center">
              <Calendar className="text-burgundy-600 mr-2" size={16} />
              <span className="text-sm font-medium text-burgundy-700">Possession</span>
            </div>
            <span className="text-sm font-bold text-burgundy-600">{project.possession}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 lg:gap-3">
          <Button
            className="flex-1 bg-burgundy-600 hover:bg-burgundy-700 text-white rounded-full font-medium text-sm transition-colors duration-200"
            asChild
          >
            <Link href={`/projects/${project.slug}`}>View Details</Link>
          </Button>
          <Button
            variant="outline"
            className="flex-1 sm:flex-none border-burgundy-200 text-burgundy-600 hover:bg-burgundy-50 rounded-full font-medium bg-transparent text-sm transition-colors duration-200"
            onClick={() => handleWhatsAppInquiry(project.title, project.location, project.price, project.slug)}
          >
            Enquire
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <main ref={mainRef} className="min-h-screen w-full" style={{ overflowX: 'hidden' }}>
        {/* Header Section */}
        <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <Building2 size={16} className="mr-2" />
              Our Projects
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Premium <span className="text-yellow-300">Properties</span> Portfolio
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Discover exceptional new projects and premium resale properties across Ahmedabad's most desirable locations.
            </p>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="py-20 bg-textured-light projects-grid-section">
        <div className="container mx-auto px-4">
          <div className="animate-on-scroll opacity-0 translate-y-8">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="flex justify-center mb-8">
                <div className="flex bg-white rounded-full p-1 shadow-lg border border-gray-200">
                  <button
                    onClick={() => setActiveTab("new-projects")}
                    className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
                      activeTab === "new-projects"
                        ? "bg-burgundy-600 text-white shadow-md"
                        : "text-gray-600 hover:text-burgundy-600"
                    }`}
                  >
                    <Sparkles size={16} />
                    New Projects
                  </button>
                  <button
                    onClick={() => setActiveTab("resale-properties")}
                    className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
                      activeTab === "resale-properties"
                        ? "bg-burgundy-600 text-white shadow-md"
                        : "text-gray-600 hover:text-burgundy-600"
                    }`}
                  >
                    <Home size={16} />
                    Resale Properties
                  </button>
                </div>
              </div>

              {/* Search and Filters */}
              <div className="mb-8 space-y-6">
                {/* Search Bar with Filter Button */}
                <div className="flex justify-center">
                  <div className="flex items-center gap-3">
                    {/* Search Bar */}
                    <div className="relative w-96">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                      <Input
                        placeholder="Search by property name or location..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-10 py-2 h-10 rounded-full border border-gray-200 focus:border-burgundy-500 focus:ring-1 focus:ring-burgundy-200 transition-all duration-300 text-sm"
                      />
                      {searchTerm && (
                        <button
                          onClick={() => setSearchTerm("")}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                        >
                          <X size={16} />
                        </button>
                      )}
                    </div>

                    {/* Filter Button */}
                    <Button
                      variant="outline"
                      onClick={() => setShowFilters(!showFilters)}
                      className="px-4 py-2 h-10 rounded-full border border-gray-200 hover:border-burgundy-500 hover:bg-burgundy-50 transition-all duration-300 text-sm"
                    >
                      <Filter size={16} className="mr-1" />
                      Filters
                    </Button>
                  </div>
                </div>

                {/* Filter Options */}
                <div className="relative w-full">
                  <div
                    className={`transition-all duration-300 ease-in-out overflow-hidden ${
                      showFilters
                        ? 'max-h-96 opacity-100 transform translate-y-0'
                        : 'max-h-0 opacity-0 transform -translate-y-4'
                    }`}
                  >
                    <div className="bg-white rounded-xl border border-gray-100 p-4 sm:p-6 mt-4">
                      {/* Filter Header with Reset Button */}
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">Filter Properties</h3>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={resetFilters}
                          className="text-burgundy-600 hover:text-burgundy-700 hover:bg-burgundy-50 text-sm"
                        >
                          Reset All
                        </Button>
                      </div>

                      {/* Mobile: Stack filters vertically, Desktop: Grid layout */}
                      <div className="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 sm:gap-4">
                        {/* Price Filter */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 block sm:hidden">Price Range</label>
                          <Select value={priceFilter} onValueChange={setPriceFilter}>
                            <SelectTrigger className="w-full h-10 border-gray-200 focus:border-burgundy-500 focus:ring-2 focus:ring-burgundy-200 transition-all text-sm">
                              <SelectValue placeholder="All Prices" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Prices</SelectItem>
                              <SelectItem value="under-1cr">Under ₹1 Cr</SelectItem>
                              <SelectItem value="1-2cr">₹1-2 Cr</SelectItem>
                              <SelectItem value="above-2cr">Above ₹2 Cr</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Location Filter */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 block sm:hidden">Location</label>
                          <Select value={locationFilter} onValueChange={setLocationFilter}>
                            <SelectTrigger className="w-full h-10 border-gray-200 focus:border-burgundy-500 focus:ring-2 focus:ring-burgundy-200 transition-all text-sm">
                              <SelectValue placeholder="All Locations" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Locations</SelectItem>
                              <SelectItem value="Bopal">Bopal</SelectItem>
                              <SelectItem value="Shela">Shela</SelectItem>
                              <SelectItem value="Gota">Gota</SelectItem>
                              <SelectItem value="Makarba">Makarba</SelectItem>
                              <SelectItem value="Sarkhej">Sarkhej</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Type Filter */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 block sm:hidden">Property Type</label>
                          <Select value={typeFilter} onValueChange={setTypeFilter}>
                            <SelectTrigger className="w-full h-10 border-gray-200 focus:border-burgundy-500 focus:ring-2 focus:ring-burgundy-200 transition-all text-sm">
                              <SelectValue placeholder="All Types" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Types</SelectItem>
                              <SelectItem value="New Launch">New Launch</SelectItem>
                              <SelectItem value="Ready to Move">Ready to Move</SelectItem>
                              <SelectItem value="Under Construction">Under Construction</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* BHK Filter */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 block sm:hidden">BHK Configuration</label>
                          <Select value={bhkFilter} onValueChange={setBhkFilter}>
                            <SelectTrigger className="w-full h-10 border-gray-200 focus:border-burgundy-500 focus:ring-2 focus:ring-burgundy-200 transition-all text-sm">
                              <SelectValue placeholder="All BHK" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All BHK</SelectItem>
                              <SelectItem value="1 BHK">1 BHK</SelectItem>
                              <SelectItem value="2 BHK">2 BHK</SelectItem>
                              <SelectItem value="3 BHK">3 BHK</SelectItem>
                              <SelectItem value="4 BHK">4 BHK</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Project Type Filter */}
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-gray-700 block sm:hidden">Project Category</label>
                          <Select value={projectTypeFilter} onValueChange={setProjectTypeFilter}>
                            <SelectTrigger className="w-full h-10 border-gray-200 focus:border-burgundy-500 focus:ring-2 focus:ring-burgundy-200 transition-all text-sm">
                              <SelectValue placeholder="All Projects" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Projects</SelectItem>
                              <SelectItem value="Residential">Residential</SelectItem>
                              <SelectItem value="Commercial">Commercial</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* Active Filters Display (Mobile Only) */}
                      <div className="mt-4 sm:hidden">
                        {(priceFilter !== "all" || locationFilter !== "all" || typeFilter !== "all" || bhkFilter !== "all" || projectTypeFilter !== "all") && (
                          <div className="flex flex-wrap gap-2">
                            <span className="text-xs text-gray-600 mr-2">Active filters:</span>
                            {priceFilter !== "all" && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-burgundy-100 text-burgundy-800">
                                {priceFilter === "under-1cr" ? "Under ₹1 Cr" : priceFilter === "1-2cr" ? "₹1-2 Cr" : "Above ₹2 Cr"}
                                <button onClick={() => setPriceFilter("all")} className="ml-1 hover:text-burgundy-900">×</button>
                              </span>
                            )}
                            {locationFilter !== "all" && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-burgundy-100 text-burgundy-800">
                                {locationFilter}
                                <button onClick={() => setLocationFilter("all")} className="ml-1 hover:text-burgundy-900">×</button>
                              </span>
                            )}
                            {typeFilter !== "all" && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-burgundy-100 text-burgundy-800">
                                {typeFilter}
                                <button onClick={() => setTypeFilter("all")} className="ml-1 hover:text-burgundy-900">×</button>
                              </span>
                            )}
                            {bhkFilter !== "all" && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-burgundy-100 text-burgundy-800">
                                {bhkFilter}
                                <button onClick={() => setBhkFilter("all")} className="ml-1 hover:text-burgundy-900">×</button>
                              </span>
                            )}
                            {projectTypeFilter !== "all" && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-burgundy-100 text-burgundy-800">
                                {projectTypeFilter}
                                <button onClick={() => setProjectTypeFilter("all")} className="ml-1 hover:text-burgundy-900">×</button>
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tab Content */}
              <TabsContent value="new-projects" className="mt-0">
                <div className="tab-content-heading mb-8">
                  <h2 className="text-2xl lg:text-3xl font-bold text-center text-gray-900 mb-2">
                    New Launch Projects
                  </h2>
                  <p className="text-center text-gray-600">Discover the latest residential and commercial developments</p>
                </div>

                {(() => {
                  const filteredProjects = filterProperties(newProjects)
                  const totalPages = getTotalPages(filteredProjects.length)
                  const paginatedProjects = getPaginatedData(filteredProjects)

                  return (
                    <>
                      {filteredProjects.length === 0 ? (
                        <div className="text-center py-12">
                          <p className="text-gray-500 text-lg">No properties found matching your criteria.</p>
                          <Button onClick={resetFilters} className="mt-4 bg-burgundy-600 hover:bg-burgundy-700">
                            Clear Filters
                          </Button>
                        </div>
                      ) : (
                        <>
                          <div className="mb-6 text-center">
                            <p className="text-sm text-gray-600">
                              Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, filteredProjects.length)} of {filteredProjects.length} properties
                              {totalPages > 1 && (
                                <span className="ml-2 text-burgundy-600 font-medium">
                                  (Page {currentPage} of {totalPages})
                                </span>
                              )}
                            </p>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
                            {paginatedProjects.map((project, index) => renderProjectCard(project, index))}
                          </div>

                          {/* Pagination */}
                          {renderPagination(filteredProjects.length)}
                        </>
                      )}
                    </>
                  )
                })()}
              </TabsContent>

              <TabsContent value="resale-properties" className="mt-0">
                <div className="tab-content-heading mb-8">
                  <h2 className="text-2xl lg:text-3xl font-bold text-center text-gray-900 mb-2">
                    Resale Properties
                  </h2>
                  <p className="text-center text-gray-600">Premium resale properties ready for immediate possession</p>
                </div>

                {(() => {
                  const filteredProjects = filterProperties(resaleProperties)
                  const totalPages = getTotalPages(filteredProjects.length)
                  const paginatedProjects = getPaginatedData(filteredProjects)

                  return (
                    <>
                      {filteredProjects.length === 0 ? (
                        <div className="text-center py-12">
                          <p className="text-gray-500 text-lg">No properties found matching your criteria.</p>
                          <Button onClick={resetFilters} className="mt-4 bg-burgundy-600 hover:bg-burgundy-700">
                            Clear Filters
                          </Button>
                        </div>
                      ) : (
                        <>
                          <div className="mb-6 text-center">
                            <p className="text-sm text-gray-600">
                              Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, filteredProjects.length)} of {filteredProjects.length} properties
                              {totalPages > 1 && (
                                <span className="ml-2 text-burgundy-600 font-medium">
                                  (Page {currentPage} of {totalPages})
                                </span>
                              )}
                            </p>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
                            {paginatedProjects.map((project, index) => renderProjectCard(project, index))}
                          </div>

                          {/* Pagination */}
                          {renderPagination(filteredProjects.length)}
                        </>
                      )}
                    </>
                  )
                })()}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>
    </main>
  )
}
