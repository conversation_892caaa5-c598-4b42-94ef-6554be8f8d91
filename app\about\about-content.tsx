"use client"

import { useEffect, useRef } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

import Image from "next/image"
import {
  Target,
  Eye,
  BookOpen,
  Building2,
  Home,
  TrendingUp,
  Shield,
  Award,
  CheckCircle,
  Handshake,
  Star
} from "lucide-react"

export default function AboutContent() {
  const mainRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <main ref={mainRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <BookOpen size={16} className="mr-2" />
              About Us
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              About <span className="text-yellow-300">Dwelling Desire</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Your trusted partner in Ahmedabad's real estate journey with expertise, transparency, and commitment to excellence.
            </p>
          </div>
        </div>
      </section>

      {/* About Content */}
      <section className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Welcome to <span className="text-burgundy-600">Dwelling Desire</span>
              </h2>

              <div className="text-center mb-12">
                <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto mb-8">
                  At Dwelling Desire, we often say — <strong className="text-burgundy-600">"Home or office is not just a place. It's a feeling."</strong>
                </p>
                <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto mb-8">
                  That feeling of security, achievement, warmth, or growth — it's personal, and it matters.
                </p>
                <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
                  When we, Jay Dholakiya and Dhara Pujara, founded Dwelling Desire, we knew that real estate needed a fresh perspective — one rooted in integrity, empathy, and excellence. With over <strong className="text-burgundy-600">1000 clients served</strong> and a strong presence in Ahmedabad's commercial and residential markets, we are proud to be a name people trust.
                </p>
              </div>

              <div className="text-center mb-16">
                <div className="max-w-3xl mx-auto">
                  <p className="text-lg text-gray-600 leading-relaxed mb-8">
                    Every client is different. Every dream is unique. That's why we focus on understanding your needs — not just showing listings. Whether you're a first-time homebuyer, an investor, or a business looking for the perfect office, we aim to be your partners in progress.
                  </p>
                  <p className="text-lg text-gray-600 leading-relaxed mb-12">
                    Because at Dwelling Desire, your space should reflect your journey — and feel like it.
                  </p>

                  {/* Founders Section */}
                  <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
                    <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Meet Our Founders</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      {/* Jay Dholakiya */}
                      <div className="text-center">
                        <div className="relative w-32 h-32 mx-auto mb-4">
                          <Image
                            src="/images/about/jay-dholakiya.jpg"
                            alt="Jay Dholakiya - Co-Founder, Dwelling Desire"
                            width={128}
                            height={128}
                            className="w-full h-full object-cover rounded-full shadow-lg"
                          />
                        </div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">Jay Dholakiya</h4>
                        <p className="text-burgundy-600 font-medium mb-2">Co-Founder</p>
                        <p className="text-gray-600 text-sm">Dwelling Desire</p>
                      </div>

                      {/* Dhara Pujara */}
                      <div className="text-center">
                        <div className="relative w-32 h-32 mx-auto mb-4 overflow-hidden rounded-full shadow-lg">
                          <Image
                            src="/images/about/dhara-pujara.jpg"
                            alt="Dhara Pujara - Co-Founder, Dwelling Desire"
                            width={128}
                            height={128}
                            className="w-full h-full object-cover object-top"
                            style={{ objectPosition: '50% 20%' }}
                          />
                        </div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">Dhara Pujara</h4>
                        <p className="text-burgundy-600 font-medium mb-2">Co-Founder</p>
                        <p className="text-gray-600 text-sm">Dwelling Desire</p>
                      </div>
                    </div>

                    <div className="mt-8 pt-6 border-t border-gray-200">
                      <p className="text-gray-700 font-medium text-center">
                        Jay Dholakiya & Dhara Pujara<br />
                        <span className="text-burgundy-600">Founders, Dwelling Desire</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Expertise Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Award size={16} className="mr-2" />
                  Our Expertise
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  What We <span className="text-burgundy-600">Specialize In</span>
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Comprehensive real estate services tailored to meet your unique property needs
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Building2 className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">New Project Sales</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Partnering with leading developers to offer the best upcoming projects with exclusive access and competitive pricing.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Home className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Luxury & Affordable Housing</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Expertise in both premium and budget-friendly properties to match every lifestyle and budget requirement.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-green-600 to-green-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <TrendingUp className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Commercial Spaces</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Offices, retail spaces, and investment-friendly commercial properties in prime business locations.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-purple-600 to-purple-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Handshake className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Leasing & Rentals</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Helping businesses and individuals find the right spaces for rent with flexible terms and conditions.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-orange-600 to-orange-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Star className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Investment Consulting</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Strategic investment opportunities in Ahmedabad's growing real estate market with expert guidance.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Shield size={16} className="mr-2" />
                  Why Choose Us
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  Why <span className="text-burgundy-600">Dwelling Desire</span>?
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Discover what makes us Ahmedabad's most trusted real estate partner
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-burgundy-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-burgundy-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Client-Centric Approach</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Personalized solutions for buyers, sellers, and investors with dedicated support throughout your journey.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-blue-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-blue-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Market Expertise</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Deep knowledge of Ahmedabad's real estate landscape with insights into emerging trends and opportunities.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-green-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-green-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Strong Developer Network</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Exclusive access to top projects and investment deals through our established developer partnerships.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-purple-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-purple-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">End-to-End Support</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      From property selection to final transaction and after-sales assistance - we're with you every step.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-orange-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-orange-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Trusted by Leading Brands</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Our work with industry leaders and satisfied clients speaks for our commitment to excellence.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-red-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-red-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">1000+ Happy Clients</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Successfully served over 1000 clients with transparent dealings and professional service.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="grid lg:grid-cols-2 gap-12">
                {/* Vision Statement */}
                <div className="text-center lg:text-left">
                  <div className="w-16 h-16 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full flex items-center justify-center mb-6 mx-auto lg:mx-0">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
                    Our <span className="text-burgundy-600">Vision</span>
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed mb-6">
                    To become the most trusted real estate consulting firm by offering personalized, one-to-one solutions that guide our clients through every step of their property journey — from selection to possession and beyond.
                  </p>
                </div>

                {/* Mission Statement */}
                <div className="text-center lg:text-left">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mb-6 mx-auto lg:mx-0">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
                    Our <span className="text-burgundy-600">Mission</span>
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed mb-6">
                    At Dwelling Desire, our mission is to simplify real estate decisions through tailored services that include:
                  </p>
                  <ul className="text-gray-600 space-y-3 text-left">
                    <li className="flex items-start">
                      <CheckCircle className="text-burgundy-600 mr-3 mt-1 flex-shrink-0" size={16} />
                      <span>Customized property solutions for every client</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="text-burgundy-600 mr-3 mt-1 flex-shrink-0" size={16} />
                      <span>In-house Vastu consultation for harmonious living and working spaces</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="text-burgundy-600 mr-3 mt-1 flex-shrink-0" size={16} />
                      <span>Expert guidance in home loan and interior design for a complete experience</span>
                    </li>
                  </ul>
                  <p className="text-gray-600 leading-relaxed mt-6">
                    We are committed to professionalism, transparency, and long-term relationships built on trust and performance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* RERA Registration & ARA Membership Section */}
      <section className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Shield size={16} className="mr-2" />
                  Certifications & Memberships
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  <span className="text-burgundy-600">RERA Registered</span> - ARA Member
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Trusted | Transparent | Compliant
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Left Content */}
                <div className="space-y-8">
                  <div className="bg-white p-8 rounded-2xl shadow-lg">
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">
                      We follow all legal norms and are proudly associated with Ahmedabad's top realtors' network.
                    </h3>

                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="text-burgundy-600 mt-1 flex-shrink-0" size={20} />
                        <span className="text-gray-600">Trusted | Transparent | Compliant</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="text-burgundy-600 mt-1 flex-shrink-0" size={20} />
                        <span className="text-gray-600">We follow all legal norms and regulations</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="text-burgundy-600 mt-1 flex-shrink-0" size={20} />
                        <span className="text-gray-600">Proudly associated with Ahmedabad's top realtors' network</span>
                      </div>
                    </div>

                    <div className="mt-8 p-6 bg-gray-50 rounded-xl">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-burgundy-100 rounded-full flex items-center justify-center">
                          <Shield className="text-burgundy-600" size={24} />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900">RERA NO:</h4>
                          <p className="text-sm text-gray-600 font-mono break-all">
                            AG/GJ/AHMEDABAD/AHMEDABAD CITY/AUDA/AA01913/280427R1
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Content - ARA Logo */}
                <div className="text-center">
                  <div className="bg-white p-8 rounded-2xl shadow-lg">
                    <div className="mb-6">
                      <Image
                        src="/images/ara-logo.webp"
                        alt="Ahmedabad Realtors Association (ARA) Member"
                        width={300}
                        height={200}
                        className="mx-auto"
                      />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">
                      Ahmedabad Realtors Association
                    </h4>
                    <p className="text-gray-600">
                      Proud member of ARA, ensuring ethical practices and professional standards in real estate services.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
