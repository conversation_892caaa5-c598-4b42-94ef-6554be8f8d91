"use client"

import type React from "react"
import { useEffect, useRef, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { MapPin, Phone, Mail, Clock, Send, MessageCircle } from "lucide-react"

export default function Contact() {
  const sectionRef = useRef<HTMLElement>(null)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  })

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Form submitted:", formData)
  }

  return (
    <section ref={sectionRef} className="py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20 animate-on-scroll opacity-0 translate-y-8">
          <div className="inline-flex items-center space-x-2 bg-gray-100 rounded-full px-4 py-2 text-sm font-medium mb-6">
            <MessageCircle size={16} className="text-burgundy-600" />
            <span className="text-gray-700">Get in Touch</span>
          </div>

          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Let's <span className="text-burgundy-600">Connect</span>
          </h2>

          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Ready to find your dream property? Let's start the conversation.
          </p>
        </div>

        <div className="grid lg:grid-cols-5 gap-16">
          {/* Contact Information */}
          <div className="lg:col-span-2 animate-on-scroll opacity-0 translate-x-8">
            <div className="space-y-8">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>
                <p className="text-gray-600 mb-8 text-lg leading-relaxed">
                  We're here to help you with all your real estate needs.
                </p>
              </div>

              {/* Contact Cards */}
              <div className="space-y-6">
                <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-2xl">
                  <div className="bg-burgundy-100 p-3 rounded-xl">
                    <MapPin className="text-burgundy-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Office Address</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Titanium Heights, A-705,
                      <br />
                      opp. Vodafone House,
                      <br />
                      Prahlad Nagar, Ahmedabad,
                      <br />
                      Gujarat 380015
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-2xl">
                  <div className="bg-green-100 p-3 rounded-xl">
                    <Phone className="text-green-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Phone Numbers</h4>
                    <p className="text-gray-600">
                      +91 81411 12929
                      <br/>
                      +91 98986 31401
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-2xl">
                  <div className="bg-blue-100 p-3 rounded-xl">
                    <Mail className="text-blue-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Email Address</h4>
                    <p className="text-gray-600">
                      <EMAIL>
                      <br />
                      <EMAIL>
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-2xl">
                  <div className="bg-orange-100 p-3 rounded-xl">
                    <Clock className="text-orange-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Business Hours</h4>
                    <p className="text-gray-600">
                      Mon - Sat: 9:00 AM - 7:00 PM
                      <br />
                      Sunday: 10:00 AM - 5:00 PM
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-3 animate-on-scroll opacity-0 translate-x-8 delay-200">
            <Card className="border-0 shadow-xl bg-white">
              <CardContent className="p-10">
                <div className="mb-8">
                  <h3 className="text-3xl font-bold text-gray-900 mb-3">Send us a Message</h3>
                  <p className="text-gray-600 text-lg">We'll get back to you within 24 hours.</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Full Name</label>
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter your full name"
                        className="h-14 rounded-2xl border-gray-200 focus:border-burgundy-500 focus:ring-burgundy-500 focus:outline-none text-base"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Email Address</label>
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="Enter your email"
                        className="h-14 rounded-2xl border-gray-200 focus:border-burgundy-500 focus:ring-burgundy-500 focus:outline-none text-base"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Phone Number</label>
                      <Input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="Enter your phone number"
                        className="h-14 rounded-2xl border-gray-200 focus:border-burgundy-500 focus:ring-burgundy-500 focus:outline-none text-base"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Subject</label>
                      <Input
                        type="text"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        placeholder="What's this about?"
                        className="h-14 rounded-2xl border-gray-200 focus:border-burgundy-500 focus:ring-burgundy-500 focus:outline-none text-base"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">Message</label>
                    <Textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Tell us about your requirements..."
                      rows={6}
                      className="rounded-2xl border-gray-200 focus:border-burgundy-500 focus:ring-burgundy-500 focus:outline-none resize-none text-base"
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    size="lg"
                    className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white px-6 py-3 md:px-8 md:py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group h-12 md:h-14 text-sm md:text-base"
                  >
                    Send Message
                    <Send className="ml-2 group-hover:translate-x-1 transition-transform" size={16} />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Map Section */}
        <div className="mt-20 animate-on-scroll opacity-0 translate-y-8 delay-400">
          <Card className="border-0 shadow-lg overflow-hidden">
            <CardContent className="p-0">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3672.632166011687!2d72.50231910000001!3d23.000549900000003!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x395e9ba5a59f7421%3A0x48dc1bc0318363f!2sDWELLING%20DESIRE!5e0!3m2!1sen!2sin!4v1752388218786!5m2!1sen!2sin"
                width="100%"
                height="450"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Dwelling Desire Office Location"
                className="w-full h-96"
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
